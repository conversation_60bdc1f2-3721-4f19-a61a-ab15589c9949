const { network, ethers } = require("hardhat")

module.exports = async ({ getNamedAccounts, deployments }) => {
    const { deploy, log } = deployments
    const { deployer } = await getNamedAccounts()
    
    log(`Network: ${network.name}`)
    log(`Deployer: ${deployer}`)
    log(`Chain ID: ${network.config.chainId}`)
    
    // Simple test deployment
    log("Starting test deployment...")
    
    try {
        const testDeployment = await deploy("Netronlink", {
            from: deployer,
            args: [],
            log: true,
            waitConfirmations: 1,
        })
        
        log(`Test deployment successful: ${testDeployment.address}`)
        return testDeployment
    } catch (error) {
        log(`Deployment failed: ${error.message}`)
        throw error
    }
}

module.exports.tags = ["test"]
