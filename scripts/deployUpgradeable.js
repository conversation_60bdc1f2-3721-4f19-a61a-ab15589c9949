const { network, ethers } = require("hardhat")

module.exports = async ({ getNamedAccounts, deployments }) => {
    const { deploy, log } = deployments
    const { deployer } = await getNamedAccounts()

    log(`Deploying from account: ${deployer}`)

    const recipients = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ]
    const amounts = [
        ethers.parseEther("********"), // 12%
        ethers.parseEther("********"), // 15%
        ethers.parseEther("5000000"), // 5%
        ethers.parseEther("********"), // 20%
        ethers.parseEther("********"), // 10%
        ethers.parseEther("********"), // 14%
        ethers.parseEther("4000000"), // 4%
    ]

    log("Deploying Upgradeable Netronlink Token...")

    // Deploy the implementation contract
    const implementationDeployment = await deploy("Netronlink", {
        from: deployer,
        args: [], // No constructor args for upgradeable contracts
        log: true,
        waitConfirmations: network.config.waitConfirmations || 1,
    })

    // Deploy the proxy contract
    const proxyDeployment = await deploy("OwnedUpgradeabilityProxy", {
        from: deployer,
        args: [], // Proxy constructor takes no args
        log: true,
        waitConfirmations: network.config.waitConfirmations || 1,
    })

    // Get contract instances
    const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", proxyDeployment.address)
    const implementation = await ethers.getContractAt("Netronlink", implementationDeployment.address)

    // Prepare initialization data
    log("Preparing initialization data...")
    const initData = implementation.interface.encodeFunctionData("initialize", [
        recipients,
        amounts,
        deployer
    ])

    // Set the implementation and initialize in one transaction
    log("Setting implementation and initializing...")
    await proxy.upgradeToAndCall(implementationDeployment.address, initData)

    // Get the proxied contract instance
    const proxiedToken = await ethers.getContractAt("Netronlink", proxyDeployment.address)

    // Verify initialization
    log("Verifying initialization...")
    const name = await proxiedToken.name()
    const symbol = await proxiedToken.symbol()
    const totalSupply = await proxiedToken.totalSupply()
    const owner = await proxiedToken.owner()

    log(`Token Name: ${name}`)
    log(`Token Symbol: ${symbol}`)
    log(`Total Supply: ${ethers.formatEther(totalSupply)} NTL`)
    log(`Contract Owner: ${owner}`)

    log(`Netronlink Implementation deployed to: ${implementationDeployment.address}`)
    log(`Proxy deployed to: ${proxyDeployment.address}`)
    log(`Use proxy address ${proxyDeployment.address} to interact with the token`)

    // Call verification script after deployment with timeout
    log("📋 Scheduling contract verification...")

    // Import and call verification script after a delay
    setTimeout(async () => {
        try {
            const { verifyUpgradeableContracts } = require("./verify-upgradeable")

            log("🔍 Starting contract verification process...")
            await verifyUpgradeableContracts(
                implementationDeployment.address,
                proxyDeployment.address,
                recipients,
                amounts,
                deployer
            )
            log("✅ Contract verification completed successfully!")
        } catch (error) {
            log("⚠️ Contract verification failed (this is optional):", error.message)
            log("💡 You can manually verify later using: npm run verify")
        }
    }, 60000) // 60 second delay to ensure contracts are indexed

    // Return the proxy address as the main contract address
    return {
        token: proxyDeployment.address,
        implementation: implementationDeployment.address
    }
}

module.exports.tags = ["upgradeable", "proxy"] 