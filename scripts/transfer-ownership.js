const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔄 Transfer Ownership Script");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    // Contract addresses
    const PROXY_ADDRESS = "******************************************";
    const NEW_OWNER_ADDRESS = "******************************************";

    console.log("\n📋 Transfer Information:");
    console.log("Contract Address:", PROXY_ADDRESS);
    console.log("New Owner Address:", NEW_OWNER_ADDRESS);

    try {
        // Get the current signer
        const [signer] = await ethers.getSigners();
        console.log("\nConnected account:", signer.address);

        // Connect to the Netronlink contract through the proxy
        const netronlink = await ethers.getContractAt("Netronlink", PROXY_ADDRESS);
        
        // Check current owner
        console.log("\n🔍 Checking Current State...");
        const currentOwner = await netronlink.owner();
        console.log("Current Owner:", currentOwner);
        
        // Validate new owner address
        if (!ethers.isAddress(NEW_OWNER_ADDRESS)) {
            throw new Error("Invalid new owner address");
        }
        
        if (NEW_OWNER_ADDRESS === ethers.ZeroAddress) {
            throw new Error("Cannot transfer ownership to zero address");
        }

        // Check if the current signer is the owner
        if (currentOwner.toLowerCase() !== signer.address.toLowerCase()) {
            console.log("\n❌ Permission Error:");
            console.log("Current signer is not the contract owner.");
            console.log("Current Owner:", currentOwner);
            console.log("Current Signer:", signer.address);
            console.log("\n💡 Only the current owner can transfer ownership.");
            console.log("Please use the correct private key/account that owns the contract.");
            return;
        }

        // Check if already transferred to the target address
        if (currentOwner.toLowerCase() === NEW_OWNER_ADDRESS.toLowerCase()) {
            console.log("\n✅ Ownership Already Transferred!");
            console.log("The contract is already owned by:", NEW_OWNER_ADDRESS);
            return;
        }

        console.log("\n🔄 Executing Ownership Transfer...");
        console.log("From:", currentOwner);
        console.log("To:  ", NEW_OWNER_ADDRESS);

        // Estimate gas for the transaction
        try {
            const gasEstimate = await netronlink.transferOwnership.estimateGas(NEW_OWNER_ADDRESS);
            console.log("Estimated Gas:", gasEstimate.toString());
        } catch (gasError) {
            console.log("⚠️ Could not estimate gas:", gasError.message);
        }

        // Execute the transfer
        console.log("\n📤 Sending transaction...");
        const tx = await netronlink.transferOwnership(NEW_OWNER_ADDRESS);
        console.log("Transaction Hash:", tx.hash);
        console.log("⏳ Waiting for confirmation...");

        // Wait for transaction confirmation
        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed!");
        console.log("Block Number:", receipt.blockNumber);
        console.log("Gas Used:", receipt.gasUsed.toString());

        // Verify the transfer
        console.log("\n🔍 Verifying Transfer...");
        const newOwner = await netronlink.owner();
        console.log("New Owner:", newOwner);

        if (newOwner.toLowerCase() === NEW_OWNER_ADDRESS.toLowerCase()) {
            console.log("✅ Ownership Transfer Successful!");
            
            // Check for OwnershipTransferred event
            const events = receipt.logs.filter(log => {
                try {
                    const parsed = netronlink.interface.parseLog(log);
                    return parsed.name === 'OwnershipTransferred';
                } catch {
                    return false;
                }
            });

            if (events.length > 0) {
                const event = netronlink.interface.parseLog(events[0]);
                console.log("\n📋 Event Details:");
                console.log("Previous Owner:", event.args.previousOwner);
                console.log("New Owner:", event.args.newOwner);
            }
        } else {
            console.log("❌ Transfer verification failed!");
            console.log("Expected:", NEW_OWNER_ADDRESS);
            console.log("Actual:", newOwner);
        }

        // Provide summary
        console.log("\n📊 Transfer Summary:");
        console.log("========================================");
        console.log("Contract:", PROXY_ADDRESS);
        console.log("Previous Owner:", currentOwner);
        console.log("New Owner:", newOwner);
        console.log("Transaction:", tx.hash);
        console.log("Status:", newOwner.toLowerCase() === NEW_OWNER_ADDRESS.toLowerCase() ? "✅ SUCCESS" : "❌ FAILED");
        console.log("========================================");

        // Etherscan link
        console.log("\n🔗 View on Etherscan:");
        console.log(`https://sepolia.etherscan.io/tx/${tx.hash}`);

    } catch (error) {
        console.error("\n❌ Transfer failed:", error.message);
        
        if (error.message.includes("Ownable: caller is not the owner")) {
            console.log("💡 Make sure you're using the correct account that owns the contract");
        } else if (error.message.includes("insufficient funds")) {
            console.log("💡 Make sure you have enough ETH for gas fees");
        } else if (error.message.includes("nonce")) {
            console.log("💡 Try again, there might be a nonce issue");
        }
        
        throw error;
    }
}

main()
    .then(() => {
        console.log("\n✅ Transfer script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
