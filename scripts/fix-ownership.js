const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔧 Fix Ownership Script");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    // Contract addresses
    const PROXY_ADDRESS = "******************************************";
    const TARGET_OWNER = "******************************************";

    console.log("\n📋 Fix Information:");
    console.log("Proxy Address:", PROXY_ADDRESS);
    console.log("Target Owner:", TARGET_OWNER);

    try {
        // Get the current signer
        const [signer] = await ethers.getSigners();
        console.log("\nConnected account:", signer.address);

        // Connect to both proxy and token contracts
        const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", PROXY_ADDRESS);
        const netronlink = await ethers.getContractAt("Netronlink", PROXY_ADDRESS);
        
        // Check current states
        console.log("\n🔍 Checking Current State...");
        const proxyOwner = await proxy.proxyOwner();
        const tokenOwner = await netronlink.owner();
        
        console.log("Proxy Owner:", proxyOwner);
        console.log("Token Owner:", tokenOwner);
        
        // Validate target owner address
        if (!ethers.isAddress(TARGET_OWNER)) {
            throw new Error("Invalid target owner address");
        }
        
        if (TARGET_OWNER === ethers.ZeroAddress) {
            throw new Error("Cannot transfer ownership to zero address");
        }

        // Check if signer is the proxy owner (needed to call functions on behalf of the contract)
        if (proxyOwner.toLowerCase() !== signer.address.toLowerCase()) {
            console.log("\n❌ Permission Error:");
            console.log("Current signer is not the proxy owner.");
            console.log("Proxy Owner:", proxyOwner);
            console.log("Current Signer:", signer.address);
            console.log("\n💡 Only the proxy owner can fix this issue.");
            return;
        }

        // The issue: Token owner is the proxy contract itself
        // Solution: Use proxy to call transferOwnership on the token contract
        
        console.log("\n🔧 Fixing Ownership Issue...");
        console.log("Current token owner (proxy):", tokenOwner);
        console.log("Target owner:", TARGET_OWNER);

        // Encode the transferOwnership call
        const transferOwnershipData = netronlink.interface.encodeFunctionData(
            "transferOwnership", 
            [TARGET_OWNER]
        );

        console.log("\n📤 Executing ownership transfer through proxy...");
        
        // Since the proxy is the owner, we need to make a low-level call through the proxy
        // This will execute transferOwnership as if called by the proxy (current owner)
        const tx = await signer.sendTransaction({
            to: PROXY_ADDRESS,
            data: transferOwnershipData,
            gasLimit: 100000 // Set a reasonable gas limit
        });

        console.log("Transaction Hash:", tx.hash);
        console.log("⏳ Waiting for confirmation...");

        // Wait for transaction confirmation
        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed!");
        console.log("Block Number:", receipt.blockNumber);
        console.log("Gas Used:", receipt.gasUsed.toString());

        // Verify the transfer
        console.log("\n🔍 Verifying Transfer...");
        const newTokenOwner = await netronlink.owner();
        console.log("New Token Owner:", newTokenOwner);

        if (newTokenOwner.toLowerCase() === TARGET_OWNER.toLowerCase()) {
            console.log("✅ Ownership Transfer Successful!");
            
            // Check for OwnershipTransferred event
            const events = receipt.logs.filter(log => {
                try {
                    const parsed = netronlink.interface.parseLog(log);
                    return parsed.name === 'OwnershipTransferred';
                } catch {
                    return false;
                }
            });

            if (events.length > 0) {
                const event = netronlink.interface.parseLog(events[0]);
                console.log("\n📋 Event Details:");
                console.log("Previous Owner:", event.args.previousOwner);
                console.log("New Owner:", event.args.newOwner);
            }
        } else {
            console.log("❌ Transfer verification failed!");
            console.log("Expected:", TARGET_OWNER);
            console.log("Actual:", newTokenOwner);
        }

        // Final state check
        console.log("\n📊 Final State:");
        console.log("========================================");
        console.log("Proxy Owner:", await proxy.proxyOwner());
        console.log("Token Owner:", await netronlink.owner());
        console.log("Transaction:", tx.hash);
        console.log("Status:", newTokenOwner.toLowerCase() === TARGET_OWNER.toLowerCase() ? "✅ SUCCESS" : "❌ FAILED");
        console.log("========================================");

        // Etherscan link
        console.log("\n🔗 View on Etherscan:");
        console.log(`https://sepolia.etherscan.io/tx/${tx.hash}`);

        // Next steps
        console.log("\n📝 Next Steps:");
        if (newTokenOwner.toLowerCase() === TARGET_OWNER.toLowerCase()) {
            console.log("1. ✅ Token ownership is now correctly set");
            console.log("2. 🎯 The new owner can manage token functions (whitelist, etc.)");
            console.log("3. 🔧 Proxy ownership remains with deployer for upgrades");
        } else {
            console.log("1. ❌ Transfer failed - check transaction details");
            console.log("2. 🔄 You may need to try again");
        }

    } catch (error) {
        console.error("\n❌ Fix failed:", error.message);
        
        if (error.message.includes("Ownable: caller is not the owner")) {
            console.log("💡 The proxy is not the current owner, or there's a permission issue");
        } else if (error.message.includes("insufficient funds")) {
            console.log("💡 Make sure you have enough ETH for gas fees");
        } else if (error.message.includes("execution reverted")) {
            console.log("💡 The transaction was reverted - check the error details");
        }
        
        throw error;
    }
}

main()
    .then(() => {
        console.log("\n✅ Fix script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
