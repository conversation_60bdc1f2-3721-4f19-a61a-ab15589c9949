const { ethers, network } = require("hardhat");

async function main() {
    console.log("🧪 Testing deployed contract functionality...");
    console.log("Network:", network.name);
    
    // Contract address from latest deployment
    const proxyAddress = "******************************************";
    
    // Get contract instance
    const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
    const [signer] = await ethers.getSigners();
    
    console.log("Testing with account:", signer.address);
    console.log("Contract address:", proxyAddress);
    
    try {
        // Test basic ERC20 functionality
        console.log("\n📊 Basic Contract Information:");
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const decimals = await netronlink.decimals();
        const totalSupply = await netronlink.totalSupply();
        const maxSupply = await netronlink.MAX_SUPPLY();
        
        console.log("Name:", name);
        console.log("Symbol:", symbol);
        console.log("Decimals:", decimals);
        console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("Max Supply:", ethers.formatEther(maxSupply), "NTL");
        
        // Test ownership
        console.log("\n👤 Ownership Information:");
        const owner = await netronlink.owner();
        console.log("Contract Owner:", owner);
        console.log("Is proxy the owner?", owner === proxyAddress);
        
        // Test vesting information
        console.log("\n⏰ Vesting Information:");
        const vestingRecipient = await netronlink.vestingRecipient();
        const teamVesting = await netronlink.teamVesting();
        
        console.log("Vesting Recipient:", vestingRecipient);
        console.log("Team Allocation:", ethers.formatEther(teamVesting.totalAllocated), "NTL");
        console.log("Already Claimed:", ethers.formatEther(teamVesting.claimed), "NTL");
        console.log("Vesting Start Time:", new Date(Number(teamVesting.startTime) * 1000).toISOString());
        
        // Test some recipient balances
        console.log("\n💰 Recipient Balances:");
        const recipients = [
            "******************************************",
            "******************************************",
            "******************************************"
        ];
        
        for (let i = 0; i < recipients.length; i++) {
            const balance = await netronlink.balanceOf(recipients[i]);
            console.log(`Recipient ${i + 1} (${recipients[i]}): ${ethers.formatEther(balance)} NTL`);
        }
        
        // Test contract balance (should have team tokens)
        const contractBalance = await netronlink.balanceOf(proxyAddress);
        console.log(`Contract Balance: ${ethers.formatEther(contractBalance)} NTL`);
        
        // Test whitelist functionality
        console.log("\n📋 Whitelist Information:");
        const isContractWhitelisted = await netronlink.isWhitelisted(proxyAddress);
        const isSignerWhitelisted = await netronlink.isWhitelisted(signer.address);
        
        console.log("Is contract whitelisted?", isContractWhitelisted);
        console.log("Is deployer whitelisted?", isSignerWhitelisted);
        
        console.log("\n✅ All contract tests passed successfully!");
        console.log("🔗 View on Etherscan: https://sepolia.etherscan.io/address/" + proxyAddress);
        
    } catch (error) {
        console.error("❌ Contract test failed:", error.message);
        throw error;
    }
}

main()
    .then(() => {
        console.log("\n🎉 Contract testing completed successfully!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Contract testing failed:", error);
        process.exit(1);
    });
