const { ethers } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔍 Checking Private Key Address...");

    try {
        // Get the address from the private key (add 0x prefix if missing)
        let privateKey = process.env.Private_Key;
        if (!privateKey) {
            throw new Error("Private_Key not found in environment variables");
        }
        if (!privateKey.startsWith('0x')) {
            privateKey = '0x' + privateKey;
        }
        const wallet = new ethers.Wallet(privateKey);
        const address = wallet.address;

        console.log("\n📋 Address Information:");
        console.log("Private Key Address:", address);
        console.log("TEAM_FOUNDER Address:", process.env.TEAM_FOUNDER);

        // Check if they match
        const isMatch = address.toLowerCase() === process.env.TEAM_FOUNDER.toLowerCase();
        console.log("Addresses Match:", isMatch ? "✅ YES" : "❌ NO");

        if (isMatch) {
            console.log("\n✅ Perfect! The private key corresponds to the TEAM_FOUNDER address.");
            console.log("We can proceed with transferring ownership to this address.");
        } else {
            console.log("\n⚠️ The private key and TEAM_FOUNDER addresses don't match.");
            console.log("We'll transfer ownership to the private key address (the deployer).");
        }

        return address;

    } catch (error) {
        console.error("❌ Error checking private key:", error.message);
        throw error;
    }
}

main()
    .then((address) => {
        console.log(`\n✅ Address check completed: ${address}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
