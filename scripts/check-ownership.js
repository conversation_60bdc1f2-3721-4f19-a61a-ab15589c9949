const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔍 Checking Ownership Status...");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    // Contract addresses
    const PROXY_ADDRESS = "******************************************";
    const IMPLEMENTATION_ADDRESS = "******************************************";
    const NEW_OWNER_ADDRESS = "******************************************";

    console.log("\n📋 Contract Information:");
    console.log("Main Contract (Proxy):", PROXY_ADDRESS);
    console.log("Implementation:", IMPLEMENTATION_ADDRESS);
    console.log("Expected New Owner:", NEW_OWNER_ADDRESS);

    try {
        // Get the current signer
        const [signer] = await ethers.getSigners();
        console.log("\nConnected account:", signer.address);

        // Connect to the Netronlink contract through the proxy
        const netronlink = await ethers.getContractAt("Netronlink", PROXY_ADDRESS);
        
        // Check current owner
        console.log("\n🔍 Checking Current Ownership...");
        const currentOwner = await netronlink.owner();
        console.log("Current Owner:", currentOwner);
        
        // Check if ownership transfer was successful
        const isNewOwner = currentOwner.toLowerCase() === NEW_OWNER_ADDRESS.toLowerCase();
        console.log("Is New Owner Set?", isNewOwner ? "✅ YES" : "❌ NO");

        if (isNewOwner) {
            console.log("\n✅ Ownership Transfer Successful!");
            console.log("The contract ownership has been successfully transferred to:", NEW_OWNER_ADDRESS);
        } else {
            console.log("\n⚠️ Ownership Transfer Status:");
            console.log("Expected Owner:", NEW_OWNER_ADDRESS);
            console.log("Actual Owner:  ", currentOwner);
            
            if (currentOwner.toLowerCase() === signer.address.toLowerCase()) {
                console.log("💡 The current signer is still the owner. Transfer may not have been executed yet.");
            }
        }

        // Check contract basic info
        console.log("\n📊 Contract Information:");
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        
        console.log("Token Name:", name);
        console.log("Token Symbol:", symbol);
        console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");

        // Function signature information
        console.log("\n🔧 Function Information:");
        console.log("transferOwnership function signature: 0xf2fde38b");
        console.log("This corresponds to: transferOwnership(address)");
        console.log("Function comes from OpenZeppelin's OwnableUpgradeable contract");

        // Check if we can call owner-only functions with current signer
        console.log("\n🔐 Permission Check:");
        try {
            // Try to call a view function that doesn't change state
            const isWhitelisted = await netronlink.isWhitelisted(signer.address);
            console.log("Can read contract state: ✅");
            
            // Check if current signer can perform owner functions
            if (currentOwner.toLowerCase() === signer.address.toLowerCase()) {
                console.log("Current signer has owner permissions: ✅");
                console.log("💡 You can execute owner-only functions like addWhitelist, removeWhitelist, etc.");
            } else {
                console.log("Current signer has owner permissions: ❌");
                console.log("💡 Only the owner can execute owner-only functions.");
            }
        } catch (error) {
            console.log("Error checking permissions:", error.message);
        }

        // Provide next steps
        console.log("\n📝 Next Steps:");
        if (isNewOwner) {
            console.log("1. ✅ Ownership transfer is complete");
            console.log("2. 💡 The new owner can now manage the contract");
            console.log("3. 🔧 Owner functions include: addWhitelist, removeWhitelist, etc.");
        } else {
            console.log("1. 🔍 Check if the transferOwnership transaction was mined");
            console.log("2. 📋 Verify the transaction hash on Etherscan");
            console.log("3. 🔄 If needed, execute the transfer again with proper permissions");
            console.log("\n💡 To transfer ownership, the current owner should call:");
            console.log(`   await netronlink.transferOwnership("${NEW_OWNER_ADDRESS}")`);
        }

        // Etherscan links
        console.log("\n🔗 Useful Links:");
        console.log(`📊 Contract on Etherscan: https://sepolia.etherscan.io/address/${PROXY_ADDRESS}`);
        console.log(`🔍 Implementation: https://sepolia.etherscan.io/address/${IMPLEMENTATION_ADDRESS}`);
        console.log(`👤 New Owner: https://sepolia.etherscan.io/address/${NEW_OWNER_ADDRESS}`);

    } catch (error) {
        console.error("\n❌ Error checking ownership:", error.message);
        
        if (error.message.includes("network")) {
            console.log("💡 Make sure you're connected to the correct network (Sepolia)");
        } else if (error.message.includes("contract")) {
            console.log("💡 Verify the contract addresses are correct");
        }
    }
}

main()
    .then(() => {
        console.log("\n✅ Ownership check completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
