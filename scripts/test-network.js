const { ethers, network } = require("hardhat");

async function main() {
    console.log("Testing network connection...");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);
    
    const [signer] = await ethers.getSigners();
    console.log("Signer address:", signer.address);
    
    const balance = await signer.provider.getBalance(signer.address);
    console.log("Balance:", ethers.formatEther(balance), "ETH");
    
    const blockNumber = await signer.provider.getBlockNumber();
    console.log("Current block number:", blockNumber);
    
    console.log("Network test completed successfully!");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("Network test failed:", error);
        process.exit(1);
    });
