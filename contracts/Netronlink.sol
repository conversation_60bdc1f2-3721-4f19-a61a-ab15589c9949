// SPDX-License-Identifier: MIT
pragma solidity 0.8.28;
// final

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

contract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable {
    uint256 public constant INITIAL_SUPPLY = 80000000 * 1e18;
    uint256 public constant CLIFF = 365 * 24 * 60 * 60; // 365 days in seconds
    uint256 public constant MONTHLY = 30 * 24 * 60 * 60; // 30 days in seconds
    uint256 public constant TOTAL_MONTHS = 24;
    uint256 internal constant teamAmount = ******** * 1e18;
    address public vestingRecipient;

    mapping(address => bool) public isWhitelisted;

    //Struct
    struct VestingSchedule {
        uint256 totalAllocated;
        uint256 claimed;
        uint64 startTime;
    }
    VestingSchedule public teamVesting;

    //EVENTS--
    event Whitelisted(address indexed account);
    event WhitelistRemoved(address indexed account);
    event TeamTokensClaimed(address indexed recipient, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
    event PresaleTokensClaimed(address presaleWallet, uint256 amount); 

    function initialize(
        address[] memory recipients,
        uint256[] memory amounts,
        address teamAndFounder
    ) public initializer {
        __ERC20_init("Netronlink Token", "NTL");
        __Ownable_init();

        require(recipients.length == amounts.length, "Mismatched input arrays");

        uint256 totalToMint = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalToMint += amounts[i];
        }

        require(totalToMint  <= INITIAL_SUPPLY, "Exceeds initial supply");

        for (uint256 i = 0; i < recipients.length; i++) {
            _mint(recipients[i], amounts[i]);
        }

        _mint(address(this), teamAmount);

        require(teamAndFounder != address(0), "teamAndFounder cannot be zero address");
        teamVesting = VestingSchedule({
            totalAllocated: teamAmount,
            claimed: 0,
            startTime: uint64(block.timestamp)
        });
        vestingRecipient = teamAndFounder;
        addWhitelist(address(this));
    }

    function _transfer(address from, address to, uint256 amount) internal override {
        require(from != address(0), "ERC20: transfer from the zero address");
        require(to != address(0), "ERC20: transfer to the zero address");

        uint256 burnAmount = 0;
        uint256 sendAmount = amount;

        if (!isWhitelisted[from] && !isWhitelisted[to]) {
            burnAmount = amount / 100; // 1%
            sendAmount = amount - burnAmount;
        }

        // If there's a burn, handle it first by reducing the from balance
        if (burnAmount > 0) {
            _burn(from, burnAmount);
            emit TokensBurned(from, burnAmount); // burn log
        }
        
        // Call parent _transfer for the send amount
        super._transfer(from, to, sendAmount);
    }

    function addWhitelist(address user) public onlyOwner {
        isWhitelisted[user] = true;
        emit Whitelisted(user);
    }

    function removeWhitelist(address user) public onlyOwner {
        isWhitelisted[user] = false;
        emit WhitelistRemoved(user);
    }

    function claimTeamTokens() external {
        require(msg.sender == vestingRecipient, "Not authorized");

        uint256 claimable = _calculateClaimable();
        require(claimable > 0, "Nothing to claim");

        teamVesting.claimed += claimable;
        _transfer(address(this), msg.sender, claimable);

        emit TeamTokensClaimed(msg.sender, claimable);
    }

    function _calculateClaimable() internal view returns (uint256 claimable) {
        VestingSchedule storage vest = teamVesting;
    
        if (block.timestamp < vest.startTime + CLIFF) {
            return 0; // still in cliff
        }

        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;
        if (monthsElapsed > TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;

        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;
        if (monthsElapsed == TOTAL_MONTHS) {
            totalVested = vest.totalAllocated;
        }

        if (totalVested <= vest.claimed) {
            return 0;
        }

        return totalVested - vest.claimed;
    }

    function getClaimableTokens() external view returns (uint256) { 
        require(msg.sender == vestingRecipient, "Not authorized");
        return _calculateClaimable();
    }

    // Version function for upgrade tracking
    function version() external pure returns (string memory) {
        return "1.0.0";
    }

    // Gap for future storage variables in upgrades
    uint256[45] private __gap;
}
