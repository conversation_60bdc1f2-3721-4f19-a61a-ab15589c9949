# Netronlink Token Test Scenarios

This document outlines all test scenarios implemented for the Netronlink smart contract.

## Contract Overview

The Netronlink token is an upgradeable ERC20 token with:

- 100M max supply (MAX_SUPPLY)
- 80M initial supply for recipients (INITIAL_SUPPLY)
- 20M tokens allocated to team with vesting
- 1% burn mechanism for non-whitelisted transfers
- 365-day cliff + 24-month linear vesting for team tokens
- Whitelist functionality to exempt addresses from burn

## Test Files Structure

### 1. `Netronlink.test.js` - Main Functionality Tests

#### 1.1 Initialization Tests ✅

- **Valid Initialization**: Verifies correct setup with recipients and amounts
- **Array Mismatch Failure**: Tests failure when recipient/amount arrays don't match
- **Initial Supply Exceeded**: Tests failure when initial distribution > 80M (INITIAL_SUPPLY)
- **Team Vesting Setup**: Verifies team vesting structure is correctly initialized
- **Contract Whitelisting**: Confirms contract address is whitelisted by default

#### 1.2 Transfer & Burn Mechanism Tests ✅

- **Whitelisted ↔ Whitelisted**: No burn applied (both addresses whitelisted)
- **Whitelisted → Non-whitelisted**: No burn applied (sender whitelisted)
- **Non-whitelisted → Whitelisted**: No burn applied (receiver whitelisted)
- **Non-whitelisted ↔ Non-whitelisted**: 1% burn applied
- **Burn Event Emission**: Verifies `TokensBurned` event is emitted
- **Zero Address Transfer**: Tests failure when transferring to zero address
- **Zero Amount Transfer**: Handles zero amount transfers correctly

#### 1.3 Whitelist Management Tests ✅

- **Owner Add Whitelist**: Owner can add addresses with `Whitelisted` event
- **Owner Remove Whitelist**: Owner can remove addresses with `WhitelistRemoved` event
- **Non-owner Access Control**: Non-owners cannot modify whitelist
- **Whitelist Effect Verification**: Confirms whitelist status affects burn behavior

#### 1.4 Team Vesting & Claiming Tests ✅

- **Cliff Period Enforcement**: Cannot claim before 365-day cliff
- **Unauthorized Claiming**: Only `vestingRecipient` can claim
- **Post-Cliff Monthly Claims**: Correct proportional claiming after cliff + months
- **Progressive Claims**: Multiple claims work correctly over time
- **Full Vesting**: Can claim all 20M tokens after cliff + 24 months
- **Over-claiming Prevention**: Cannot claim more than allocated
- **Claim Event Emission**: `TeamTokensClaimed` events emitted correctly

#### 1.5 View Functions Tests ✅

- **Version Function**: Returns correct version "1.0.0"
- **Constants Verification**: MAX_SUPPLY, CLIFF, MONTHLY, TOTAL_MONTHS
- **Claimable Tokens**: `getClaimableTokens()` returns correct amounts
- **Access Control**: Non-authorized users cannot call `getClaimableTokens()`

#### 1.6 Edge Cases & Security Tests ✅

- **Large Transfer Handling**: Handles transfers close to user's full balance
- **Vesting Precision**: Tests mathematical precision in vesting calculations
- **Total Supply Integrity**: Maintains correct total supply through multiple operations

### 2. `Netronlink.upgrade.test.js` - Upgradeable Contract Tests

#### 2.1 Upgrade Functionality ✅

- **State Preservation**: All contract state maintained after upgrade
- **Functionality Preservation**: All functions work correctly post-upgrade
- **Double Initialization Prevention**: Cannot re-initialize after upgrade
- **Ownership Maintenance**: Owner privileges preserved through upgrade

#### 2.2 Proxy Security ✅

- **Admin Access Control**: Only proxy admin can perform upgrades
- **Storage Layout Compatibility**: Validates storage layout compatibility

## Test Execution Commands

```bash
# Run all tests
npm test

# Run specific test file
npx hardhat test test/unit/Netronlink.test.js

# Run upgrade tests
npx hardhat test test/unit/Netronlink.upgrade.test.js

# Run with coverage
npm run coverage

# Run with gas reporting
npx hardhat test --gas-reporter
```

## Key Test Constants

```javascript
const teamAmount = ethers.parseEther("20000000") // 20M tokens
const maxSupply = ethers.parseEther("100000000") // 100M tokens (MAX_SUPPLY)
const initialSupply = ethers.parseEther("80000000") // 80M tokens (INITIAL_SUPPLY)
const cliff = 365 * 24 * 60 * 60 // 365 days in seconds
const monthly = 30 * 24 * 60 * 60 // 30 days in seconds
const totalMonths = 24 // Total vesting months
```

## Expected Behaviors

### Transfer Burn Logic

```
if (!isWhitelisted[from] && !isWhitelisted[to]) {
    burnAmount = amount / 100; // 1%
    sendAmount = amount - burnAmount;
}
```

### Vesting Calculation

```
monthsElapsed = (currentTime - startTime - CLIFF) / MONTHLY
totalVested = (totalAllocated * monthsElapsed) / TOTAL_MONTHS
claimable = totalVested - claimed
```

## Coverage Goals

- **Line Coverage**: >95%
- **Branch Coverage**: >90%
- **Function Coverage**: 100%
- **Statement Coverage**: >95%

## Test Data Setup

Each test uses:

- Owner: deployer account
- Team Wallet: dedicated team vesting recipient
- User1: 10M initial tokens
- User2: 5M initial tokens
- User3: 0 initial tokens (for testing)

## Critical Security Scenarios Tested

1. **Access Control**: Only authorized users can call restricted functions
2. **Reentrancy**: Transfer function handles reentrant calls safely
3. **Integer Overflow**: Large number operations handled correctly
4. **State Consistency**: Contract state remains consistent after operations
5. **Upgrade Safety**: Contract upgrades preserve all existing functionality

## Integration Points

- OpenZeppelin ERC20Upgradeable
- OpenZeppelin OwnableUpgradeable
- OpenZeppelin Initializable
- Hardhat Network Helpers (time manipulation)
- Chai Assertion Library
